import { db } from "../../db";
import { and, desc, eq, inArray, sql } from "drizzle-orm";
import {
  h_applications,
  h_applicationDocuments,
  h_applicationDecisions,
} from "@/drizzle/h_schema/applications";
import { h_listings } from "@/drizzle/h_schema/listings";
import { h_vehicleCatalog } from "@/drizzle/h_schema/vehicle-catalog";
import { party, individual, users, vehicles } from "@/drizzle/schema";
import { vehicleMake, vehicleModel } from "@/drizzle/schema";

// ==================== TYPES ====================

export interface AdminApplicationWithDetails {
  id: number;
  applicantId: number;
  listingId: number;
  applicationDetails: any;
  createdAt: string;

  // Listing information
  listing: {
    id: number;
    listingType: string;
    listingDetails: any;
    sourceType: string;
    sourceId: number;
  };

  // Applicant information
  applicant: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };

  // Vehicle/Catalog information
  vehicle?: {
    make: string;
    model: string;
    year: number;
    registration?: string;
  };

  catalog?: {
    make: string;
    model: string;
    year: number;
    category: string;
    weeklyFeeTarget?: number;
  };

  // Documents
  documents: Array<{
    id: number;
    documentType: string;
    documentUrl: string;
    uploadedAt: string;
  }>;

  // Latest decision
  latestDecision?: {
    decision: string;
    reason?: string;
    decisionAt: string;
    reviewerName?: string;
  };
}

export interface AdminApplicationFilters {
  status?: "all" | "pending" | "under_review" | "approved" | "rejected";
  applicationType?: "all" | "ehailing-platform" | "rental" | "fractional";
  searchQuery?: string;
  dateFrom?: string;
  dateTo?: string;
}

// ==================== ADMIN APPLICATION FUNCTIONS ====================

/**
 * Get all applications for admin review with full details
 */
export async function getAllApplicationsForAdmin(
  filters?: AdminApplicationFilters
): Promise<AdminApplicationWithDetails[]> {
  try {
    // Base query to get applications with listing and applicant info
    let query = db
      .select({
        // Application fields
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,
        createdAt: sql<string>`${h_applications.id}::text`, // Using ID as placeholder for createdAt

        // Listing fields
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,

        // Applicant fields
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: users.email,

        // Vehicle fields (when sourceType = 'vehicle')
        vehicleMake: vehicleMake.name,
        vehicleModel: vehicleModel.model,
        vehicleYear: vehicles.manufacturingYear,
        vehicleRegistration: vehicles.vehicleRegistration,

        // Catalog fields (when sourceType = 'catalog')
        catalogMake: h_vehicleCatalog.make,
        catalogModel: h_vehicleCatalog.model,
        catalogYear: h_vehicleCatalog.year,
        catalogCategory: h_vehicleCatalog.category,
        catalogWeeklyFeeTarget: h_vehicleCatalog.weeklyFeeTarget,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(users, eq(party.externalId, users.username))
      // Left join for vehicle data (when sourceType = 'vehicle')
      .leftJoin(
        vehicles,
        and(
          eq(h_listings.sourceType, "vehicle"),
          eq(h_listings.sourceId, vehicles.id)
        )
      )
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      // Left join for catalog data (when sourceType = 'catalog')
      .leftJoin(
        h_vehicleCatalog,
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listings.sourceId, h_vehicleCatalog.id)
        )
      );

    // Apply filters
    const whereConditions = [];

    if (filters?.applicationType && filters.applicationType !== "all") {
      whereConditions.push(eq(h_listings.listingType, filters.applicationType));
    }

    if (whereConditions.length > 0) {
      query = query.where(and(...whereConditions));
    }

    const applications = await query.orderBy(desc(h_applications.id));

    if (applications.length === 0) {
      return [];
    }

    // Get application IDs for fetching related data
    const applicationIds = applications.map((app) => app.id);

    // Get documents for all applications
    const documents = await db
      .select()
      .from(h_applicationDocuments)
      .where(inArray(h_applicationDocuments.applicationId, applicationIds));

    // Get latest decisions for all applications
    const decisions = await db
      .select({
        applicationId: h_applicationDecisions.applicationId,
        decision: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        decisionAt: h_applicationDecisions.decisionAt,
        reviewerName: sql<string>`COALESCE(${individual.firstName} || ' ' || ${individual.lastName}, ${users.username}, 'Unknown')`,
      })
      .from(h_applicationDecisions)
      .leftJoin(users, eq(h_applicationDecisions.reviewerId, users.id))
      .leftJoin(party, eq(users.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(inArray(h_applicationDecisions.applicationId, applicationIds))
      .orderBy(desc(h_applicationDecisions.decisionAt));

    // Group documents and decisions by application ID
    const documentsByApp = documents.reduce(
      (acc, doc) => {
        if (!acc[doc.applicationId]) acc[doc.applicationId] = [];
        acc[doc.applicationId].push(doc);
        return acc;
      },
      {} as Record<number, typeof documents>
    );

    const latestDecisionsByApp = decisions.reduce(
      (acc, decision) => {
        if (!acc[decision.applicationId]) {
          acc[decision.applicationId] = decision;
        }
        return acc;
      },
      {} as Record<number, (typeof decisions)[0]>
    );

    // Transform to final format
    const result: AdminApplicationWithDetails[] = applications.map((app) => {
      const appDocuments = documentsByApp[app.id] || [];
      const latestDecision = latestDecisionsByApp[app.id];

      return {
        id: app.id,
        applicantId: app.applicantId,
        listingId: app.listingId,
        applicationDetails: app.applicationDetails
          ? JSON.parse(app.applicationDetails)
          : {},
        createdAt: new Date().toISOString(), // Placeholder - should be from temporal fields

        listing: {
          id: app.listingId,
          listingType: app.listingType,
          listingDetails: app.listingDetails
            ? JSON.parse(app.listingDetails)
            : {},
          sourceType: app.sourceType,
          sourceId: app.sourceId,
        },

        applicant: {
          id: app.applicantId,
          firstName: app.firstName || "Unknown",
          lastName: app.lastName || "User",
          email: app.email || "<EMAIL>",
        },

        // Include vehicle info if sourceType is 'vehicle'
        ...(app.sourceType === "vehicle" &&
          app.vehicleMake && {
            vehicle: {
              make: app.vehicleMake,
              model: app.vehicleModel || "Unknown Model",
              year: app.vehicleYear || 0,
              registration: app.vehicleRegistration || undefined,
            },
          }),

        // Include catalog info if sourceType is 'catalog'
        ...(app.sourceType === "catalog" &&
          app.catalogMake && {
            catalog: {
              make: app.catalogMake,
              model: app.catalogModel || "Unknown Model",
              year: app.catalogYear || 0,
              category: app.catalogCategory || "Unknown",
              weeklyFeeTarget: app.catalogWeeklyFeeTarget || undefined,
            },
          }),

        documents: appDocuments.map((doc) => ({
          id: doc.id,
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          uploadedAt: doc.uploadedAt,
        })),

        latestDecision: latestDecision
          ? {
              decision: latestDecision.decision,
              reason: latestDecision.reason || undefined,
              decisionAt: latestDecision.decisionAt,
              reviewerName: latestDecision.reviewerName || undefined,
            }
          : undefined,
      };
    });

    // Apply client-side filters for complex filtering
    let filteredResult = result;

    if (filters?.status && filters.status !== "all") {
      filteredResult = filteredResult.filter((app) => {
        const status = app.latestDecision?.decision || "pending";
        return status === filters.status;
      });
    }

    if (filters?.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filteredResult = filteredResult.filter((app) => {
        const applicantName =
          `${app.applicant.firstName} ${app.applicant.lastName}`.toLowerCase();
        const vehicleName = app.vehicle
          ? `${app.vehicle.make} ${app.vehicle.model}`
          : "";
        const catalogName = app.catalog
          ? `${app.catalog.make} ${app.catalog.model}`
          : "";

        return (
          applicantName.includes(query) ||
          app.applicant.email.toLowerCase().includes(query) ||
          vehicleName.toLowerCase().includes(query) ||
          catalogName.toLowerCase().includes(query)
        );
      });
    }

    return filteredResult;
  } catch (error) {
    console.error("Error fetching applications for admin:", error);
    throw new Error("Failed to fetch applications for admin");
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStatisticsForAdmin(): Promise<{
  total: number;
  pending: number;
  underReview: number;
  approved: number;
  rejected: number;
  byType: Record<string, number>;
}> {
  try {
    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(h_applications);

    const total = totalResult[0]?.count || 0;

    // Get counts by latest decision status
    const statusCounts = await db
      .select({
        decision: h_applicationDecisions.decision,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .leftJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .groupBy(h_applicationDecisions.decision);

    // Get counts by listing type
    const typeCounts = await db
      .select({
        listingType: h_listings.listingType,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .groupBy(h_listings.listingType);

    // Process status counts
    const statusMap = statusCounts.reduce(
      (acc, item) => {
        acc[item.decision || "pending"] = item.count;
        return acc;
      },
      {} as Record<string, number>
    );

    // Process type counts
    const typeMap = typeCounts.reduce(
      (acc, item) => {
        acc[item.listingType] = item.count;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      total,
      pending: statusMap.pending || 0,
      underReview: statusMap.under_review || 0,
      approved: statusMap.approved || 0,
      rejected: statusMap.rejected || 0,
      byType: typeMap,
    };
  } catch (error) {
    console.error("Error fetching application statistics:", error);
    throw new Error("Failed to fetch application statistics");
  }
}
