"use client";

import Image from "next/image";
import { ArrowLeft, DollarSign } from "lucide-react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
import ApplicationStatusTimeline from "@/components/ApplicationStatusTimeline";
import { useEffect, useState } from "react";
import { getApplicationStatusTimelineAction } from "@/actions/applications";

interface Application {
  id: number;
  applicantId: number;
  listingId: number;
  applicationDetails: any;
  createdAt: string;
  listing: {
    id: number;
    listingType: string;
    listingDetails: any;
  };
  applicant: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  documents: {
    id: number;
    documentType: string;
    documentUrl: string;
    uploadedAt: string;
    status?: string;
  }[];
  latestDecision?: {
    decision: string;
    reason?: string;
    decisionAt: string;
  };
}

interface TimelineItem {
  id: number;
  status: string;
  reason?: string | null;
  timestamp: Date;
  reviewerName?: string | null;
}

interface ApplicationStatusSheetProps {
  isOpen: boolean;
  onClose: () => void;
  application: Application | null;
}

export default function ApplicationStatusSheet({
  isOpen,
  onClose,
  application,
}: ApplicationStatusSheetProps) {
  const [timeline, setTimeline] = useState<TimelineItem[]>([]);
  const [currentStatus, setCurrentStatus] = useState<string>("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (application && isOpen) {
      fetchApplicationTimeline();
    }
  }, [application, isOpen]);

  const fetchApplicationTimeline = async () => {
    if (!application) return;

    setLoading(true);
    try {
      const result = await getApplicationStatusTimelineAction(application.id);
      if (result.success && result.timeline) {
        setTimeline(result.timeline);
        // Set current status to the most recent status
        if (result.timeline.length > 0) {
          setCurrentStatus(result.timeline[0].status);
        }
      }
    } catch (error) {
      console.error("Error fetching timeline:", error);
    } finally {
      setLoading(false);
    }
  };

  if (!application) return null;

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "under_review":
        return "text-blue-600 bg-blue-100";
      case "approved":
        return "text-green-600 bg-green-100";
      case "rejected":
        return "text-red-600 bg-red-100";
      case "withdrawn":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending Review";
      case "under_review":
        return "Under Review";
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      case "withdrawn":
        return "Withdrawn";
      default:
        return status;
    }
  };

  const getApplicationTitle = () => {
    const vehicleInfo = application.listing?.listingDetails?.vehicleInfo;
    if (!vehicleInfo) return `Application #${application.id}`;
    return `${vehicleInfo.make} ${vehicleInfo.model} ${vehicleInfo.year}`;
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Application Status
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Track your application progress
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Application Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md overflow-hidden">
                {/* Vehicle Image */}
                {application.listing?.listingDetails?.vehicleInfo && (
                  <div className="h-32 bg-gray-100 relative overflow-hidden">
                    <Image
                      src={application.listing.listingDetails.vehicleInfo.image}
                      alt={`${application.listing.listingDetails.vehicleInfo.make} ${application.listing.listingDetails.vehicleInfo.model}`}
                      fill
                      className="object-contain"
                    />
                    <div className="absolute top-3 right-3 bg-[#009639] px-2 py-1 rounded-full">
                      <span className="text-xs font-medium text-white">
                        {application.listing.listingType
                          .charAt(0)
                          .toUpperCase() +
                          application.listing.listingType.slice(1)}
                      </span>
                    </div>
                  </div>
                )}

                <div className="p-4">
                  <div className="flex justify-between items-start gap-3 mb-2">
                    <h3 className="text-lg font-bold text-[#333333] flex-1 min-w-0">
                      {getApplicationTitle()}
                    </h3>
                    <span
                      className={`text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${getStatusColor(currentStatus)}`}
                    >
                      {getStatusText(currentStatus)}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                        <span className="text-[#009639] font-bold text-xs">
                          #
                        </span>
                      </div>
                      <span className="text-[#797879]">Application ID: </span>
                      <span className="text-[#333333] font-medium">
                        {application.id}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                        <DollarSign size={10} className="text-[#009639]" />
                      </div>
                      <span className="text-[#797879]">Submitted: </span>
                      <span className="text-[#333333] font-medium">
                        {formatDate(new Date(application.createdAt))}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Timeline */}
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">
                    Loading timeline...
                  </p>
                </div>
              ) : (
                <ApplicationStatusTimeline
                  timeline={timeline}
                  currentStatus={currentStatus}
                />
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
