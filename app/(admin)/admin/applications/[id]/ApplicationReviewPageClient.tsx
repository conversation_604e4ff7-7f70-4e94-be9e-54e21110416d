"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  Eye,
  Clock,
  Star,
  Car,
  Phone,
  Mail,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import type { ApplicationWithDetails } from "@/types/applications";
import {
  makeApplicationDecisionAction,
  updateDocumentStatusAction,
} from "@/actions/applications";
import { useFormState } from "react-dom";
import { toast } from "sonner";

export default function ApplicationReviewPageClient({
  application,
}: {
  application: ApplicationWithDetails;
}) {
  const [currentTab, setCurrentTab] = useState("overview");
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);

  // Helper function to format dates consistently
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Form states for server actions
  const [decisionState, decisionAction] = useFormState(
    makeApplicationDecisionAction,
    null
  );
  const [documentState, documentAction] = useFormState(
    updateDocumentStatusAction,
    null
  );

  // Helper functions to extract data from the application
  const getApplicantName = () => {
    return `${application.applicant.firstName} ${application.applicant.lastName}`;
  };

  const getVehicleName = () => {
    if (application.listing.vehicle) {
      return `${application.listing.vehicle.make} ${application.listing.vehicle.model} ${application.listing.vehicle.year}`;
    }
    if (application.listing.catalog) {
      return `${application.listing.catalog.make} ${application.listing.catalog.model} ${application.listing.catalog.year}`;
    }
    return "Unknown Vehicle";
  };

  const getCurrentStatus = () => {
    return application.latestDecision?.decision || "pending";
  };

  const getWeeklyRate = () => {
    return application.listing.catalog?.weeklyFeeTarget || 0;
  };

  // Remove the mock data function - we now use real data

  const handleDecision = (decision: "approved" | "rejected") => {
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = async () => {
    if (!pendingDecision) return;

    const formData = new FormData();
    formData.append("applicationId", application.id.toString());
    formData.append("decision", pendingDecision);
    if (decisionReason) {
      formData.append("reason", decisionReason);
    }

    try {
      await decisionAction(formData);

      setIsDecisionDialogOpen(false);
      setPendingDecision(null);
      setDecisionReason("");

      toast.success(`Application ${pendingDecision} successfully`);

      // Redirect back to applications list
      window.location.href = "/admin/applications";
    } catch (error) {
      toast.error("Failed to update application status");
    }
  };

  const handleDocumentVerification = async (
    documentId: number,
    status: "verified" | "rejected"
  ) => {
    const formData = new FormData();
    formData.append("documentId", documentId.toString());
    formData.append("status", status);

    try {
      await documentAction(formData);
      toast.success(`Document ${status} successfully`);
    } catch (error) {
      toast.error("Failed to update document status");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/applications">
              <ArrowLeft size={16} />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {application.listing.sourceType === "vehicle"
                ? "Vehicle Listing Review"
                : "Application Review"}
            </h1>
            <p className="text-gray-600 mt-1">
              {getApplicantName()} • {getVehicleName()}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={`${getStatusColor(getCurrentStatus())} px-3 py-1`}
          >
            <span className="capitalize">
              {getCurrentStatus().replace("_", " ")}
            </span>
          </Badge>
          {getCurrentStatus() === "pending" ||
          getCurrentStatus() === "under_review" ? (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => handleDecision("rejected")}
              >
                <XCircle size={16} className="mr-2" />
                Reject
              </Button>
              <Button
                className="bg-[#009639] hover:bg-[#007A2F]"
                onClick={() => handleDecision("approved")}
              >
                <CheckCircle size={16} className="mr-2" />
                Approve
              </Button>
            </div>
          ) : null}
        </div>
      </div>

      {/* Application Status Alert */}
      {application.status === "pending" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="text-yellow-600 mr-3" size={20} />
              <div>
                <p className="font-medium text-yellow-800">
                  Application Pending Review
                </p>
                <p className="text-sm text-yellow-700">
                  This application is waiting for your review and decision.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="decision">Decision</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Applicant Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    {application.listing.sourceType === "vehicle"
                      ? "Vehicle Owner Profile"
                      : "Applicant Profile"}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Full Name
                      </Label>
                      <p className="text-lg font-medium">
                        {getApplicantName()}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Application Date
                      </Label>
                      <p className="text-lg">
                        {formatDate(application.createdAt)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Email Address
                      </Label>
                      <div className="flex items-center">
                        <Mail size={16} className="text-gray-400 mr-2" />
                        <p>{application.applicant.email}</p>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </Label>
                      <div className="flex items-center">
                        <Phone size={16} className="text-gray-400 mr-2" />
                        <p>{application.applicant.phoneNumber}</p>
                      </div>
                    </div>
                    {/* Additional fields for EARN side applications */}
                    {(application.applicationType === "rental_application" ||
                      application.applicationType ===
                        "co_ownership_application") && (
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Age
                          </Label>
                          <p className="text-lg">{application.applicantAge}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Gender
                          </Label>
                          <p className="text-lg capitalize">
                            {application.applicantGender}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Driving Experience
                          </Label>
                          <p className="text-lg">
                            {application.drivingExperienceYears} years
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Documents Status
                          </Label>
                          <p
                            className={`text-lg ${application.documentsVerified ? "text-green-600" : "text-yellow-600"}`}
                          >
                            {application.documentsVerified
                              ? "All Verified"
                              : "Pending Verification"}
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car size={20} className="text-[#009639]" />
                    Vehicle Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Vehicle
                      </Label>
                      <p className="text-lg font-medium">{getVehicleName()}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Listing Type
                      </Label>
                      <p className="text-lg capitalize">
                        {application.listing.listingType}
                      </p>
                    </div>
                    {application.listing.catalog && (
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Weekly Rate
                          </Label>
                          <p className="text-lg">
                            R{getWeeklyRate().toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Initiation Fee
                          </Label>
                          <p className="text-lg">
                            R
                            {application.listing.catalog.initiationFeeTarget?.toLocaleString() ||
                              0}
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                  {application.applicationDetails && (
                    <div className="mt-4">
                      <Label className="text-sm font-medium text-gray-500">
                        Application Details
                      </Label>
                      <p className="text-sm text-gray-700 mt-1">
                        {JSON.stringify(
                          application.applicationDetails,
                          null,
                          2
                        )}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText size={20} className="text-[#009639]" />
                    Document Verification
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {application.documents.map((doc) => {
                      const isVerified = doc.status === "verified";
                      const isRejected = doc.status === "rejected";
                      const isPending = !doc.status || doc.status === "pending";

                      return (
                        <div
                          key={doc.id}
                          className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow bg-white"
                        >
                          <div className="flex items-center justify-between mb-4">
                            <h4 className="text-lg font-semibold text-gray-900">
                              {doc.documentType}
                            </h4>
                            <div
                              className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium ${
                                isVerified
                                  ? "text-green-700 bg-green-100"
                                  : isRejected
                                    ? "text-red-700 bg-red-100"
                                    : "text-yellow-700 bg-yellow-100"
                              }`}
                            >
                              {isVerified ? (
                                <CheckCircle size={16} />
                              ) : isRejected ? (
                                <XCircle size={16} />
                              ) : (
                                <Clock size={16} />
                              )}
                              <span>
                                {isVerified
                                  ? "Verified"
                                  : isRejected
                                    ? "Rejected"
                                    : "Pending"}
                              </span>
                            </div>
                          </div>
                          {doc.documentUrl && (
                            <div className="space-y-3">
                              {/* Document Actions Row */}
                              <div className="flex flex-wrap gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-2"
                                  onClick={() =>
                                    window.open(doc.documentUrl, "_blank")
                                  }
                                >
                                  <Eye size={16} />
                                  View Document
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center gap-2"
                                  onClick={() => {
                                    const link = document.createElement("a");
                                    link.href = doc.documentUrl;
                                    link.download = `${doc.documentType}.pdf`;
                                    link.click();
                                  }}
                                >
                                  <Download size={16} />
                                  Download
                                </Button>
                              </div>

                              {/* Verification Actions Row */}
                              <div className="border-t border-gray-100 pt-3">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium text-gray-700">
                                    Document Verification:
                                  </span>
                                  <div className="flex gap-2">
                                    {!isVerified && (
                                      <>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="flex items-center gap-1 text-green-600 border-green-200 hover:bg-green-50"
                                          onClick={() =>
                                            handleDocumentVerification(
                                              doc.id,
                                              "verified"
                                            )
                                          }
                                        >
                                          <CheckCircle size={14} />
                                          Verify
                                        </Button>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
                                          onClick={() =>
                                            handleDocumentVerification(
                                              doc.id,
                                              "rejected"
                                            )
                                          }
                                        >
                                          <XCircle size={14} />
                                          Reject
                                        </Button>
                                      </>
                                    )}

                                    {/* Reset verification if already verified or rejected */}
                                    {(isVerified || isRejected) && (
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                        onClick={() =>
                                          handleDocumentVerification(
                                            doc.id,
                                            "pending"
                                          )
                                        }
                                      >
                                        <Clock size={14} />
                                        Reset
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="decision" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle size={20} className="text-[#009639]" />
                    Application Decision
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label
                      htmlFor="decision-reason"
                      className="text-sm font-medium"
                    >
                      Decision Reason (Optional)
                    </Label>
                    <Textarea
                      id="decision-reason"
                      value={decisionReason}
                      onChange={(e) => setDecisionReason(e.target.value)}
                      placeholder="Add a note about your decision..."
                      className="mt-2"
                      rows={4}
                    />
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      variant="outline"
                      className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                      onClick={() => handleDecision("rejected")}
                    >
                      <XCircle size={16} className="mr-2" />
                      Reject Application
                    </Button>
                    <Button
                      className="flex-1 bg-[#009639] hover:bg-[#007A2F]"
                      onClick={() => handleDecision("approved")}
                    >
                      <CheckCircle size={16} className="mr-2" />
                      Approve Application
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Quick Info & Actions */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Application Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <Badge
                  variant="outline"
                  className={`${getStatusColor(application.status)}`}
                >
                  {application.status.replace("_", " ")}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Documents</span>
                <span className="text-sm font-medium">
                  {application.documents.filter((d) => d.uploaded).length}/
                  {application.documents.length} uploaded
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Verified</span>
                <span className="text-sm font-medium">
                  {
                    application.documents.filter((d) => d.verified === true)
                      .length
                  }
                  /{application.documents.filter((d) => d.uploaded).length}{" "}
                  verified
                </span>
              </div>
              {application.applicationType === "vehicle_lease" &&
                application.experience && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      E-hailing Experience
                    </span>
                    <span className="text-sm font-medium">
                      {application.experience.hasExperience ? "Yes" : "No"}
                    </span>
                  </div>
                )}
              {(application.applicationType === "rental_application" ||
                application.applicationType === "co_ownership_application") && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    Driving Experience
                  </span>
                  <span className="text-sm font-medium">
                    {application.drivingExperienceYears} years
                  </span>
                </div>
              )}
              {application.applicationType === "vehicle_listing" && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">Vehicle Type</span>
                  <span className="text-sm font-medium">
                    {application.make} {application.model}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Mail size={16} className="mr-2" />
                Contact Applicant
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Download size={16} className="mr-2" />
                Download All Documents
              </Button>
            </CardContent>
          </Card>

          {/* Application Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-[#009639] rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium">Application Submitted</p>
                    <p className="text-xs text-gray-500">
                      {formatShortDate(application.applicationDate)}
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Documents Uploaded
                    </p>
                    <p className="text-xs text-gray-500">
                      {application.documents.filter((d) => d.uploaded).length}{" "}
                      of {application.documents.length} completed
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-gray-300 rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">
                      Under Review
                    </p>
                    <p className="text-xs text-gray-500">
                      Pending admin decision
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Decision Confirmation Dialog */}
      <Dialog
        open={isDecisionDialogOpen}
        onOpenChange={setIsDecisionDialogOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
              Application
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {pendingDecision} this application from{" "}
              {application.applicantName}?
              {pendingDecision === "approved" && (
                <span className="block mt-2 text-sm">
                  This will notify the applicant and initiate the lease process.
                </span>
              )}
              {pendingDecision === "rejected" && (
                <span className="block mt-2 text-sm">
                  This will notify the applicant that their application was not
                  successful.
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          {decisionReason && (
            <div className="py-4">
              <Label className="text-sm font-medium">Decision Reason:</Label>
              <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded-md">
                {decisionReason}
              </p>
            </div>
          )}

          <DialogFooter className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsDecisionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className={
                pendingDecision === "approved"
                  ? "bg-[#009639] hover:bg-[#007A2F]"
                  : "bg-red-600 hover:bg-red-700"
              }
              onClick={confirmDecision}
            >
              {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
              Application
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
