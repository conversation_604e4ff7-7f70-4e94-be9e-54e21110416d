import React from "react";
import { notFound } from "next/navigation";
import { getApplicationById } from "@/drizzle-actions/applications";
import ApplicationReviewPageClient from "./ApplicationReviewPageClient";

export default async function ApplicationReviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  try {
    const applicationId = parseInt(id);
    if (isNaN(applicationId)) {
      notFound();
    }

    const result = await getApplicationById(applicationId);

    if (!result.success || !result.application) {
      notFound();
    }

    return <ApplicationReviewPageClient application={result.application} />;
  } catch (error) {
    console.error("Error fetching application data:", error);
    notFound();
  }
}
