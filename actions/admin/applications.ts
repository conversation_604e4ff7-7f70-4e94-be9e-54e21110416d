"use server";

import { revalidatePath } from "next/cache";
import {
  getAllApplicationsForAdmin,
  getApplicationStatisticsForAdmin,
  type AdminApplicationWithDetails,
  type AdminApplicationFilters,
} from "@/drizzle-actions/admin/applications";

// ==================== SERVER ACTIONS ====================

/**
 * Get all applications for admin with optional filters
 */
export async function getAllApplicationsForAdminAction(
  filters?: AdminApplicationFilters
): Promise<{
  success: boolean;
  applications?: AdminApplicationWithDetails[];
  error?: string;
}> {
  try {
    const applications = await getAllApplicationsForAdmin(filters);
    
    return {
      success: true,
      applications,
    };
  } catch (error) {
    console.error("Error in getAllApplicationsForAdminAction:", error);
    return {
      success: false,
      error: "Failed to fetch applications",
    };
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStatisticsForAdminAction(): Promise<{
  success: boolean;
  statistics?: {
    total: number;
    pending: number;
    underReview: number;
    approved: number;
    rejected: number;
    byType: Record<string, number>;
  };
  error?: string;
}> {
  try {
    const statistics = await getApplicationStatisticsForAdmin();
    
    return {
      success: true,
      statistics,
    };
  } catch (error) {
    console.error("Error in getApplicationStatisticsForAdminAction:", error);
    return {
      success: false,
      error: "Failed to fetch application statistics",
    };
  }
}

/**
 * Refresh admin applications data
 */
export async function refreshAdminApplicationsAction(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate the admin applications page
    revalidatePath("/admin/applications");
    
    return {
      success: true,
    };
  } catch (error) {
    console.error("Error in refreshAdminApplicationsAction:", error);
    return {
      success: false,
      error: "Failed to refresh applications data",
    };
  }
}
